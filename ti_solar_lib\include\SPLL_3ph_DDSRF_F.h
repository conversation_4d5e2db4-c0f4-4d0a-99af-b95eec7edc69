/*
 * =====================================================================================
 * 文件名: SPLL_3ph_DDSRF_F.h
 * 功能描述: 三相双同步参考坐标系锁相环(Three-Phase Dual Decoupled Synchronous Reference Frame PLL)
 *
 * =====================================================================================
 * 【理论基础与数学原理】
 * =====================================================================================
 *
 * 1. 锁相环(PLL)基本原理:
 *    PLL是一个反馈控制系统，用于使本地振荡器的频率和相位与输入信号同步
 *    基本方程: θ_error = θ_input - θ_output
 *    当θ_error = 0时，实现完全同步
 *
 * 2. 三相电网的数学模型:
 *    三相电压: Va = V*cos(ωt + φ)
 *             Vb = V*cos(ωt + φ - 2π/3)
 *             Vc = V*cos(ωt + φ + 2π/3)
 *    其中: V - 电压幅值, ω - 角频率, φ - 初始相位角, t - 时间
 *
 * 3. Clarke变换(ABC→αβ):
 *    将三相静止坐标系转换为两相静止坐标系
 *    Vα = (2/3)*Va - (1/3)*Vb - (1/3)*Vc
 *    Vβ = (√3/3)*Vb - (√3/3)*Vc
 *    目的: 简化三相系统的数学分析
 *
 * 4. Park变换(αβ→dq):
 *    将两相静止坐标系转换为两相旋转坐标系
 *    Vd = Vα*cos(θ) + Vβ*sin(θ)     // d轴: 与参考矢量同向(直轴分量)
 *    Vq = -Vα*sin(θ) + Vβ*cos(θ)    // q轴: 与参考矢量正交(交轴分量)
 *    当θ与电网相位同步时，Vq = 0，Vd = 电压幅值
 *
 * 5. 正负序分离理论:
 *    三相不平衡时，电压包含正序分量(+)和负序分量(-)
 *    正序: 按A→B→C顺序旋转，频率为+ω
 *    负序: 按A→C→B顺序旋转，频率为-ω
 *    在dq坐标系中，负序分量表现为2倍频(2ω)的交流分量
 *
 * 6. 双同步参考坐标系(DDSRF)原理:
 *    同时建立正序旋转坐标系(+ω)和负序旋转坐标系(-ω)
 *    正序坐标系: 正序分量→直流，负序分量→2ω交流
 *    负序坐标系: 负序分量→直流，正序分量→2ω交流
 *    通过解耦网络消除2ω交流分量的相互干扰
 *
 * =====================================================================================
 * 【工程应用背景】
 * =====================================================================================
 *
 * 主要功能:
 * - 实现三相电网电压的相位和频率检测
 * - 采用双同步参考坐标系(DDSRF)技术，提高不平衡电网条件下的性能
 * - 用于太阳能逆变器与电网的精确同步
 * - 支持电网频率变化和电压不平衡的鲁棒性检测
 *
 * 技术特点:
 * - 基于正负序分离的双DQ坐标变换
 * - 内置解耦网络消除正负序分量间的相互干扰
 * - 多级低通滤波器提高噪声抑制能力
 * - 自适应频率跟踪，适应电网频率波动
 *
 * 适用场景:
 * - 三相并网逆变器的电网同步
 * - 电网电压不平衡条件下的相位检测
 * - 微电网和分布式发电系统
 * - 有源电力滤波器的基波检测
 * - 电网故障检测与保护系统
 * =====================================================================================
 */

#ifndef SPLL_3ph_DDSRF_F_H_
#define SPLL_3ph_DDSRF_F_H_

//*********** 结构体定义 ********//

/**
 * =====================================================================================
 * @brief 锁相环环路滤波器系数结构体
 * @details 定义环路滤波器的传递函数系数，用于控制锁相环的动态响应特性
 * =====================================================================================
 *
 * 【数学模型】
 * 环路滤波器传递函数: H(z) = (B0 + B1*z^-1) / (1 + A1*z^-1)
 * 差分方程: y[n] = B0*x[n] + B1*x[n-1] - A1*y[n-1]
 *
 * 其中:
 * - x[n]: 当前输入信号 (相位误差信号)
 * - x[n-1]: 前一次输入信号
 * - y[n]: 当前输出信号 (频率控制信号)
 * - y[n-1]: 前一次输出信号
 * - z^-1: 单位延迟算子 (表示延迟一个采样周期)
 *
 * 【系数含义】
 * B0, B1: 分子系数，决定输入信号的权重
 * A1: 分母系数，决定反馈强度，通常A1 = -1.0
 *
 * 【设计原则】
 * - B0 + B1: 决定稳态增益
 * - B1/B0: 决定零点位置，影响瞬态响应
 * - A1: 决定极点位置，影响稳定性
 */
typedef struct
{
    float32 B1_lf;      // 环路滤波器分子系数B1 (前一采样点权重系数)
                        // 物理意义: 控制对历史误差信号的响应强度
                        // 典型值: 0.001 ~ 0.1 (根据期望的动态响应调整)
                        // 数据类型: float32 (32位浮点数，精度约7位有效数字)

    float32 B0_lf;      // 环路滤波器分子系数B0 (当前采样点权重系数)
                        // 物理意义: 控制对当前误差信号的响应强度
                        // 典型值: 0.01 ~ 1.0 (通常B0 > B1，保证系统稳定性)
                        // 工程考虑: B0越大响应越快，但可能引起振荡

    float32 A1_lf;      // 环路滤波器分母系数A1 (反馈系数)
                        // 物理意义: 控制输出信号的反馈强度
                        // 固定值: -1.0 (保证系统为因果稳定系统)
                        // 数学意义: 使传递函数分母为 (1 - z^-1)，实现积分特性
} SPLL_3ph_DDSRF_F_LPF_COEFF;

/**
 * =====================================================================================
 * @brief 三相双同步参考坐标系锁相环主结构体
 * @details 包含锁相环运算所需的所有状态变量、系数和中间计算结果
 * =====================================================================================
 *
 * 【算法工作流程】
 * 1. 三相电压采样: Va, Vb, Vc → ADC数字化
 * 2. Clarke变换: ABC → αβ坐标系
 * 3. 正负序分离: αβ → (d_p,q_p) + (d_n,q_n)
 * 4. 解耦网络: 消除正负序分量间的2ω频率耦合干扰
 * 5. 低通滤波: 滤除高频噪声、开关谐波和残余交流分量
 * 6. 环路滤波: PI控制器产生频率误差控制信号
 * 7. VCO积分: 频率信号积分得到相位角θ
 * 8. 三角函数计算: 生成cos(θ), sin(θ), cos(2θ), sin(2θ)
 *
 * 【数据流向图】
 * Va,Vb,Vc → Clarke → αβ → Park(+ω) → d_p,q_p ↘
 *                         ↘ Park(-ω) → d_n,q_n → 解耦网络 → 低通滤波 → 环路滤波 → VCO → θ
 */
typedef struct
{
    // =====================================================================================
    // ========== 正负序DQ分量 (算法输入信号) ==========
    // =====================================================================================

    float32 d_p;        // 正序d轴分量 (Positive sequence d-axis component)
                        // 物理意义: 三相电网电压正序分量在d轴(直轴)上的投影
                        // 数学表达: d_p = V_pos * cos(φ_error)，其中φ_error为相位误差
                        // 取值范围: -1.0 ~ +1.0 (标幺值，基准为电网电压幅值)
                        // 工程意义: 当锁相环完全同步时，d_p = 1.0，q_p = 0
                        // 数据来源: 通过ABC_DQ0_POS_NEG变换模块计算得到

    float32 d_n;        // 负序d轴分量 (Negative sequence d-axis component)
                        // 物理意义: 三相电网电压负序分量在d轴上的投影
                        // 数学表达: d_n = V_neg * cos(φ_neg)，φ_neg为负序相位
                        // 取值范围: -1.0 ~ +1.0 (标幺值)
                        // 工程意义: 理想平衡电网时d_n = 0，电网不平衡时d_n ≠ 0
                        // 应用场景: 用于检测电网不平衡程度和故障诊断

    float32 q_p;        // 正序q轴分量 (Positive sequence q-axis component)
                        // 物理意义: 三相电网电压正序分量在q轴(交轴)上的投影
                        // 数学表达: q_p = V_pos * sin(φ_error)，直接反映相位误差
                        // 取值范围: -1.0 ~ +1.0 (标幺值)
                        // 控制目标: 锁相环的控制目标是使q_p → 0
                        // 误差信号: q_p就是锁相环的相位误差信号，送入PI控制器

    float32 q_n;        // 负序q轴分量 (Negative sequence q-axis component)
                        // 物理意义: 三相电网电压负序分量在q轴上的投影
                        // 数学表达: q_n = V_neg * sin(φ_neg)
                        // 取值范围: -1.0 ~ +1.0 (标幺值)
                        // 工程意义: 用于负序分量的相位检测和补偿控制

    // =====================================================================================
    // ========== 解耦网络输出 (Decoupling Network Output) ==========
    // =====================================================================================

    float32 d_p_decoupl;    // 解耦后的正序d轴分量 (Decoupled positive sequence d-axis)
                            // 数学表达: d_p_decoupl = d_p - 解耦补偿项
                            // 解耦公式: d_p_decoupl = d_p - (d_n_lpf*cos(2θ) + q_n_lpf*sin(2θ))
                            // 物理意义: 消除负序分量对正序d轴的2ω频率干扰
                            // 工程目标: 使正序d轴分量变为纯直流，便于后续滤波和控制

    float32 d_n_decoupl;    // 解耦后的负序d轴分量 (Decoupled negative sequence d-axis)
                            // 数学表达: d_n_decoupl = d_n - 解耦补偿项
                            // 解耦公式: d_n_decoupl = d_n - (d_p_lpf*cos(2θ) - q_p_lpf*sin(2θ))
                            // 物理意义: 消除正序分量对负序d轴的2ω频率干扰
                            // 应用价值: 提高负序分量检测精度，用于不平衡补偿

    float32 q_p_decoupl;    // 解耦后的正序q轴分量 (Decoupled positive sequence q-axis)
                            // 数学表达: q_p_decoupl = q_p - 解耦补偿项
                            // 解耦公式: q_p_decoupl = q_p + (d_n_lpf*sin(2θ) - q_n_lpf*cos(2θ))
                            // 控制意义: 这是锁相环的核心误差信号，送入PI控制器
                            // 控制目标: 通过反馈控制使q_p_decoupl → 0，实现相位同步

    float32 q_n_decoupl;    // 解耦后的负序q轴分量 (Decoupled negative sequence q-axis)
                            // 数学表达: q_n_decoupl = q_n - 解耦补偿项
                            // 解耦公式: q_n_decoupl = q_n - (d_p_lpf*sin(2θ) + q_p_lpf*cos(2θ))
                            // 物理意义: 消除正序分量对负序q轴的干扰
                            // 扩展应用: 可用于负序电压的相位控制和补偿

    // =====================================================================================
    // ========== 解耦网络三角函数 (Decoupling Trigonometric Functions) ==========
    // =====================================================================================

    float32 cos_2theta;     // cos(2θ) - 二倍频余弦函数
                            // 数学表达: cos_2theta = cos(2 * theta[0])
                            // 物理意义: 用于正负序坐标变换的二倍频余弦分量
                            // 计算时机: 在每次SPLL运算开始前，基于当前θ值计算
                            // 精度要求: 需要高精度三角函数库，影响解耦效果
                            // 取值范围: -1.0 ~ +1.0

    float32 sin_2theta;     // sin(2θ) - 二倍频正弦函数
                            // 数学表达: sin_2theta = sin(2 * theta[0])
                            // 物理意义: 用于正负序坐标变换的二倍频正弦分量
                            // 理论基础: 基于正负序分量在dq坐标系中的2ω频率特性
                            // 工程实现: 通常使用查表法或CORDIC算法快速计算
                            // 同步要求: 必须与theta[0]保持严格同步

    // =====================================================================================
    // ========== 低通滤波器状态变量 (Low Pass Filter State Variables) ==========
    // =====================================================================================

    float32 y[2];       // 正序d轴低通滤波器状态数组 [当前值y[1], 前一值y[0]]
                        // 滤波器方程: y[1] = k1*input - k2*y[0]
                        // 输出计算: output = y[1] + y[0]
                        // 状态更新: y[0] = y[1] (为下次计算准备)
                        // 物理意义: 存储d_p_decoupl的滤波器内部状态
                        // 数组索引: [0]=前一次状态, [1]=当前计算状态

    float32 x[2];       // 正序q轴低通滤波器状态数组 [当前值x[1], 前一值x[0]]
                        // 滤波器方程: x[1] = k1*q_p_decoupl - k2*x[0]
                        // 输出计算: q_p_decoupl_lpf = x[1] + x[0]
                        // 控制重要性: 这个滤波器的输出直接影响锁相环性能
                        // 设计考虑: 截止频率需要平衡噪声抑制和动态响应

    float32 w[2];       // 负序d轴低通滤波器状态数组 [当前值w[1], 前一值w[0]]
                        // 滤波器方程: w[1] = k1*d_n_decoupl - k2*w[0]
                        // 输出计算: d_n_decoupl_lpf = w[1] + w[0]
                        // 应用场景: 用于电网不平衡检测和补偿控制
                        // 精度要求: 负序分量通常较小，需要高精度滤波

    float32 z[2];       // 负序q轴低通滤波器状态数组 [当前值z[1], 前一值z[0]]
                        // 滤波器方程: z[1] = k1*q_n_decoupl - k2*z[0]
                        // 输出计算: q_n_decoupl_lpf = z[1] + z[0]
                        // 扩展功能: 可用于负序无功功率检测

    float32 k1;         // 低通滤波器输入增益系数 (Input gain coefficient)
                        // 数学意义: 控制输入信号对滤波器输出的贡献权重
                        // 设计公式: k1 = (1 - k2) * α，其中α为滤波强度参数
                        // 取值范围: 0.001 ~ 0.5 (典型值: 0.01 ~ 0.1)
                        // 工程权衡: k1越大响应越快，但噪声抑制能力越差
                        // 调试指导: 电网噪声大时减小k1，动态响应要求高时增大k1

    float32 k2;         // 低通滤波器反馈增益系数 (Feedback gain coefficient)
                        // 数学意义: 控制滤波器历史状态对当前输出的影响
                        // 设计关系: k2 = 1 - k1 - ε，其中ε为小的稳定裕度
                        // 取值范围: 0.5 ~ 0.999 (典型值: 0.9 ~ 0.99)
                        // 稳定性: k2越接近1.0，滤波器越稳定但响应越慢
                        // 截止频率: fc ≈ k1*fs/(2π)，其中fs为采样频率

    // =====================================================================================
    // ========== 滤波后的解耦分量 (Filtered Decoupled Components) ==========
    // =====================================================================================

    float32 d_p_decoupl_lpf;    // 低通滤波后的正序d轴分量
                                // 计算公式: d_p_decoupl_lpf = y[1] + y[0]
                                // 物理意义: 经过解耦和滤波的纯净正序d轴直流分量
                                // 理想值: 电网电压标幺值 (通常为1.0)
                                // 应用: 用于电网电压幅值检测和电压跌落保护
                                // 精度: 直接影响电网同步质量和功率计算精度

    float32 d_n_decoupl_lpf;    // 低通滤波后的负序d轴分量
                                // 计算公式: d_n_decoupl_lpf = w[1] + w[0]
                                // 物理意义: 电网负序电压的d轴分量
                                // 理想值: 0 (完全平衡电网时)
                                // 故障检测: |d_n_decoupl_lpf| > 阈值时表示电网不平衡
                                // 补偿控制: 用于有源滤波器的负序补偿算法

    float32 q_p_decoupl_lpf;    // 低通滤波后的正序q轴分量
                                // 计算公式: q_p_decoupl_lpf = x[1] + x[0]
                                // 控制意义: 锁相环的核心反馈信号
                                // 控制目标: 通过PI控制使其趋于0
                                // 误差指标: |q_p_decoupl_lpf| < 0.01时认为锁相成功
                                // 动态性能: 其变化速度反映锁相环的跟踪能力

    float32 q_n_decoupl_lpf;    // 低通滤波后的负序q轴分量
                                // 计算公式: q_n_decoupl_lpf = z[1] + z[0]
                                // 物理意义: 电网负序电压的q轴分量
                                // 应用扩展: 可用于负序功率计算和补偿控制

    // =====================================================================================
    // ========== 环路滤波器和VCO (Loop Filter and VCO) ==========
    // =====================================================================================

    float32 v_q[2];     // 环路滤波器输入信号数组 [当前值v_q[0], 前一值v_q[1]]
                        // 信号来源: v_q[0] = q_p_decoupl (相位误差信号)
                        // 物理意义: 反映电网相位与本地相位的偏差
                        // 控制理论: 这是PI控制器的输入误差信号
                        // 数值范围: -1.0 ~ +1.0 (标幺值)
                        // 稳态值: 锁相成功时v_q[0] ≈ 0

    float32 theta[2];   // 输出相位角数组 [当前值theta[0], 前一值theta[1]] (弧度)
                        // 积分公式: theta[0] = theta[1] + 2π*fo*delta_T
                        // 物理意义: 锁相环输出的电网相位角
                        // 取值范围: 0 ~ 2π (通过模运算限制)
                        // 应用: 用于生成与电网同步的正弦波参考信号
                        // 精度要求: 相位精度直接影响并网电流的THD

    float32 ylf[2];     // 环路滤波器输出数组 [当前值ylf[0], 前一值ylf[1]] (频率误差)
                        // PI公式: ylf[0] = ylf[1] + B0*v_q[0] + B1*v_q[1]
                        // 物理意义: 频率误差控制信号 (Hz)
                        // 控制作用: 用于调节VCO的输出频率
                        // 稳态值: 电网频率稳定时ylf[0] ≈ 0
                        // 动态范围: 通常限制在 ±5Hz 以内

    float32 fo;         // 输出频率 (Hz) - 检测到的电网频率
                        // 计算公式: fo = fn + ylf[0] (带自适应滤波)
                        // 物理意义: 锁相环检测到的实时电网频率
                        // 标准值: 50Hz (中国/欧洲) 或 60Hz (美国/日本)
                        // 变化范围: 通常在额定频率的 ±2% 以内
                        // 应用: 频率保护、功率控制、电网质量监测

    float32 fn;         // 标称频率 (Hz) - 电网额定频率
                        // 设定值: 50.0 (中国电网) 或 60.0 (美国电网)
                        // 物理意义: 电网的额定工作频率
                        // 初始化: 在SPLL_3ph_DDSRF_F_init()中设置
                        // 不变性: 运行过程中保持常数
                        // 基准作用: VCO的频率基准和频率误差计算基准

    float32 delta_T;    // 采样周期 (秒) - 控制算法的执行周期
                        // 典型值: 50e-6 (对应20kHz PWM频率)
                        // 物理意义: 锁相环算法的执行时间间隔
                        // 设计考虑: 需要满足奈奎斯特采样定理
                        // VCO积分: 用于相位角的数值积分计算
                        // 性能影响: 采样频率越高，锁相精度越高

    // =====================================================================================
    // ========== 环路滤波器系数 (Loop Filter Coefficients) ==========
    // =====================================================================================

    SPLL_3ph_DDSRF_F_LPF_COEFF lpf_coeff;  // 环路滤波器传递函数系数结构体
                                           // 包含成员: B0_lf, B1_lf, A1_lf
                                           // 设计目的: 决定锁相环的动态响应特性
                                           // 调试重要性: 这些系数直接影响锁相速度和稳定性
                                           // 初始化: 在SPLL_3ph_DDSRF_F_init()中计算和设置
                                           // 设计方法: 通常基于期望的带宽和阻尼比设计

} SPLL_3ph_DDSRF_F;  // 三相双同步参考坐标系锁相环完整数据结构

//*********** 函数声明 *******//

/**
 * =====================================================================================
 * @brief 三相双同步参考坐标系锁相环初始化函数
 * @details 初始化锁相环的所有状态变量和系数，为正常运行做准备
 * =====================================================================================
 *
 * 【函数功能详解】
 * 1. 设置电网标称频率和采样周期等基本参数
 * 2. 初始化所有状态变量为零或合适的初值
 * 3. 计算和设置低通滤波器系数
 * 4. 计算和设置环路滤波器PI控制器系数
 * 5. 为锁相环的稳定运行做好准备
 *
 * 【参数详细说明】
 * @param Grid_freq 电网标称频率 (Hz) - 整数类型
 *                  数值选择: 50 (中国、欧洲、印度等) 或 60 (美国、日本、韩国等)
 *                  物理意义: 电网的额定工作频率，作为锁相环的频率基准
 *                  设计考虑: 不同地区的电网频率标准不同，需要正确设置
 *                  影响范围: 影响VCO的中心频率和频率检测范围
 *                  错误后果: 设置错误会导致锁相环无法正常工作
 *
 * @param DELTA_T   采样周期 (秒) - 浮点数类型
 *                  计算公式: DELTA_T = 1 / PWM_frequency
 *                  典型值: 50e-6 (20kHz PWM), 100e-6 (10kHz PWM), 25e-6 (40kHz PWM)
 *                  物理意义: 控制算法的执行时间间隔
 *                  设计约束: 必须满足 DELTA_T < 1/(10*Grid_freq) 以保证稳定性
 *                  精度要求: 影响相位积分精度，建议使用双精度计算
 *                  实时性: 必须与实际的中断周期严格一致
 *
 * @param k1        低通滤波器输入增益系数 - 浮点数类型
 *                  数学意义: 控制输入信号对滤波器输出的贡献权重
 *                  取值范围: 0.001 ~ 0.5 (推荐: 0.01 ~ 0.1)
 *                  设计公式: k1 = 2π*fc*DELTA_T / (1 + 2π*fc*DELTA_T)
 *                  其中fc为期望的截止频率 (Hz)
 *                  工程权衡:
 *                    - k1大: 响应快，但噪声抑制差
 *                    - k1小: 噪声抑制好，但响应慢
 *                  调试指导:
 *                    - 电网噪声大时: k1 = 0.01 ~ 0.05
 *                    - 动态响应要求高时: k1 = 0.05 ~ 0.1
 *
 * @param k2        低通滤波器反馈增益系数 - 浮点数类型
 *                  数学意义: 控制滤波器历史状态对当前输出的影响
 *                  设计关系: k2 = 1 - k1 - ε (ε为稳定裕度，通常0.001~0.01)
 *                  取值范围: 0.5 ~ 0.999 (推荐: 0.9 ~ 0.99)
 *                  稳定性: k2越接近1，滤波器越稳定但响应越慢
 *                  约束条件: 必须满足 k1 + k2 < 1 以保证滤波器稳定
 *                  实际计算: 通常设为 k2 = 1 - k1 - 0.001
 *
 * @param spll      指向锁相环结构体的指针 - 指针类型
 *                  内存要求: 指向的内存必须已经分配且有效
 *                  初始化后: 结构体中的所有成员都会被正确初始化
 *                  生命周期: 在整个锁相环运行期间必须保持有效
 *                  线程安全: 如果多线程访问，需要外部同步机制
 *
 * 【初始化过程详解】
 * 1. 基本参数设置:
 *    spll->fn = (float32)Grid_freq
 *    spll->delta_T = DELTA_T
 *    spll->k1 = k1, spll->k2 = k2
 *
 * 2. 状态变量清零:
 *    所有滤波器状态数组清零: y[2], x[2], w[2], z[2]
 *    所有输出变量清零: theta[2], ylf[2], v_q[2]
 *    所有中间变量清零: d_p_decoupl_lpf等
 *
 * 3. 环路滤波器系数计算:
 *    基于期望的锁相环带宽和阻尼比计算B0_lf, B1_lf, A1_lf
 *    典型设计: 带宽 = 10~50Hz, 阻尼比 = 0.707
 *
 * 4. 初始频率设置:
 *    spll->fo = spll->fn (初始输出频率等于标称频率)
 *
 * @note 调用此函数后，锁相环处于初始状态，需要2-5个电网周期才能稳定锁定
 * @warning 必须在使用SPLL_3ph_DDSRF_F_FUNC或SPLL_3ph_DDSRF_F_MACRO之前调用此初始化函数
 * @warning 参数k1和k2必须满足稳定性条件: k1 > 0, k2 > 0, k1 + k2 < 1
 * @warning Grid_freq只能是50或60，其他值可能导致不可预期的行为
 */
void SPLL_3ph_DDSRF_F_init(int Grid_freq, float32 DELTA_T, float32 k1, float32 k2, SPLL_3ph_DDSRF_F *spll);

/**
 * =====================================================================================
 * @brief 三相双同步参考坐标系锁相环主运算函数
 * @details 执行完整的锁相环算法，包括解耦、滤波、环路滤波和VCO运算
 * =====================================================================================
 *
 * 【函数调用时序】
 * 1. ADC中断触发 (每个PWM周期)
 * 2. 三相电压采样: Va, Vb, Vc
 * 3. Clarke变换: ABC → Vα, Vβ
 * 4. 正负序分离: αβ → d_p, q_p, d_n, q_n
 * 5. 计算二倍频三角函数: cos_2theta, sin_2theta
 * 6. 调用本函数: SPLL_3ph_DDSRF_F_FUNC()
 * 7. 获取输出: theta[0], fo
 * 8. 生成同步信号用于逆变器控制
 *
 * 【调用前必须准备的数据】
 * - spll_obj->d_p: 正序d轴分量 (标幺值, -1.0~+1.0)
 *   数据来源: ABC_DQ0_POS_NEG变换模块
 *   物理意义: 电网正序电压的同相分量
 *   质量要求: 必须是经过正确标幺化的数值
 *
 * - spll_obj->d_n: 负序d轴分量 (标幺值, -1.0~+1.0)
 *   数据来源: ABC_DQ0_POS_NEG变换模块
 *   物理意义: 电网负序电压的同相分量
 *   平衡电网: 理想情况下应接近0
 *
 * - spll_obj->q_p: 正序q轴分量 (标幺值, -1.0~+1.0)
 *   数据来源: ABC_DQ0_POS_NEG变换模块
 *   控制意义: 锁相环的主要误差信号
 *   目标值: 锁相成功时应趋于0
 *
 * - spll_obj->q_n: 负序q轴分量 (标幺值, -1.0~+1.0)
 *   数据来源: ABC_DQ0_POS_NEG变换模块
 *   物理意义: 电网负序电压的正交分量
 *   应用: 用于电网不平衡检测
 *
 * - spll_obj->cos_2theta: cos(2θ) (无量纲, -1.0~+1.0)
 *   计算公式: cos_2theta = cos(2 * theta[0])
 *   计算时机: 基于前一次的theta[0]计算
 *   精度要求: 需要高精度三角函数库
 *   更新频率: 每次调用前必须更新
 *
 * - spll_obj->sin_2theta: sin(2θ) (无量纲, -1.0~+1.0)
 *   计算公式: sin_2theta = sin(2 * theta[0])
 *   同步要求: 必须与cos_2theta同时计算
 *   实现方法: 可使用查表法或CORDIC算法
 *
 * 【函数执行的详细步骤】
 *
 * 步骤1: 解耦网络运算 (Decoupling Network)
 * -----------------------------------------------
 * 目的: 消除正负序分量间的2ω频率相互干扰
 * 数学原理: 基于旋转坐标变换理论
 * 计算公式:
 *   d_p_decoupl = d_p - (d_n_lpf*cos(2θ) + q_n_lpf*sin(2θ))
 *   q_p_decoupl = q_p + (d_n_lpf*sin(2θ) - q_n_lpf*cos(2θ))
 *   d_n_decoupl = d_n - (d_p_lpf*cos(2θ) - q_p_lpf*sin(2θ))
 *   q_n_decoupl = q_n - (d_p_lpf*sin(2θ) + q_p_lpf*cos(2θ))
 *
 * 步骤2: 低通滤波运算 (Low Pass Filtering)
 * -----------------------------------------------
 * 目的: 滤除高频噪声、开关谐波和残余交流分量
 * 滤波器类型: 一阶IIR低通滤波器
 * 数学模型: y[n] = k1*x[n] - k2*y[n-1], output = y[n] + y[n-1]
 * 对四个解耦分量分别进行滤波处理
 *
 * 步骤3: 环路滤波运算 (Loop Filtering)
 * -----------------------------------------------
 * 目的: 将相位误差转换为频率控制信号
 * 控制器类型: PI (比例积分) 控制器
 * 输入信号: q_p_decoupl (相位误差)
 * 传递函数: H(z) = (B0 + B1*z^-1) / (1 + A1*z^-1)
 * 输出信号: ylf[0] (频率误差控制信号)
 *
 * 步骤4: VCO积分运算 (Voltage Controlled Oscillator)
 * -----------------------------------------------
 * 目的: 将频率信号积分得到相位角
 * 频率计算: fo = fn + ylf[0] (带自适应滤波)
 * 相位积分: theta[0] = theta[1] + 2π*fo*delta_T
 * 相位限制: 0 ≤ theta[0] < 2π (模运算)
 *
 * 【输出结果详解】
 * - spll_obj->theta[0]: 当前检测到的电网相位角
 *   单位: 弧度 (radian)
 *   范围: 0 ~ 2π
 *   精度: 通常精度优于 ±0.1° (±0.0017 rad)
 *   应用: 用于生成与电网同步的正弦波参考
 *
 * - spll_obj->fo: 当前检测到的电网频率
 *   单位: 赫兹 (Hz)
 *   范围: 通常在额定频率的 ±5% 以内
 *   精度: 通常精度优于 ±0.01 Hz
 *   应用: 频率保护、功率控制、电网质量监测
 *
 * @param spll_obj 指向锁相环结构体的指针
 *                 内存要求: 必须指向有效的已初始化结构体
 *                 修改范围: 函数会修改结构体中的多个成员变量
 *                 线程安全: 非线程安全，需要外部同步
 *
 * 【性能和实时性】
 * @note 此函数应在每个PWM周期调用一次，通常在ADC中断中执行
 * @note 函数执行时间约为10-50微秒 (取决于处理器性能)
 * @note 建议在中断优先级较高的ADC中断中调用
 * @note 如果执行时间过长，可考虑使用SPLL_3ph_DDSRF_F_MACRO宏版本
 *
 * 【输入数据质量要求】
 * @warning 输入的DQ分量必须是标准化的单位值 (标幺值)
 * @warning cos_2theta和sin_2theta必须基于当前theta值计算
 * @warning 输入数据的采样时刻必须与函数调用时刻同步
 * @warning 如果输入数据质量差，会导致锁相环性能下降或失锁
 */
void SPLL_3ph_DDSRF_F_FUNC(SPLL_3ph_DDSRF_F *spll_obj);

//*********** 宏定义 ***********//

/**
 * =====================================================================================
 * @brief 三相双同步参考坐标系锁相环快速运算宏
 * @details 提供与SPLL_3ph_DDSRF_F_FUNC函数相同功能的内联宏实现，用于对执行速度要求极高的场合
 * =====================================================================================
 *
 * 【宏的技术优势】
 * - 零函数调用开销: 代码直接内联展开，避免函数调用的压栈出栈开销
 * - 编译器优化友好: 编译器可以进行更深层次的优化，如指令重排和寄存器分配
 * - 实时性保证: 执行时间确定性更好，适合硬实时系统
 * - 缓存友好: 减少指令缓存未命中的可能性
 * - 适合高频中断: 在20kHz以上的PWM中断中使用，性能优势明显
 *
 * 【算法数学基础】
 *
 * 1. 解耦网络数学模型:
 *    基于复数理论，正负序分量在dq坐标系中的表示:
 *    正序: V+ = d_p + j*q_p (在+ω坐标系中为直流)
 *    负序: V- = d_n + j*q_n (在+ω坐标系中为2ω交流)
 *
 *    解耦变换矩阵:
 *    [d_p_decoupl]   [1   0   -cos(2θ)  -sin(2θ)] [d_p]
 *    [q_p_decoupl] = [0   1   +sin(2θ)  -cos(2θ)] [q_p]
 *    [d_n_decoupl]   [0   0   1         0       ] [d_n]
 *    [q_n_decoupl]   [0   0   0         1       ] [q_n]
 *
 *    其中θ为当前检测的相位角，2θ为二倍频分量
 *
 * 2. 低通滤波器数学模型:
 *    传递函数: H(z) = (k1) / (1 + k2*z^-1)
 *    差分方程: y[n] = k1*x[n] - k2*y[n-1]
 *    输出方程: output = y[n] + y[n-1]
 *
 *    频率响应: |H(jω)| = k1 / √(1 + (k2*ω*T)?)
 *    截止频率: fc ≈ k1*fs/(2π*k2)，其中fs为采样频率
 *
 * 3. 环路滤波器数学模型:
 *    PI控制器传递函数: H(z) = Kp + Ki/(1-z^-1)
 *    实现形式: H(z) = (B0 + B1*z^-1) / (1 + A1*z^-1)
 *    其中: B0 = Kp + Ki*T, B1 = -Kp, A1 = -1
 *
 * 4. VCO数学模型:
 *    相位积分方程: θ[n] = θ[n-1] + 2π*f*ΔT
 *    频率控制方程: f = fn + Δf，其中Δf为频率误差
 *    自适应滤波: fo += (fn + ylf[0] - fo) * α
 *    其中α为自适应系数，用于抑制频率抖动
 *
 * 【工程实现细节】
 * - 数值精度: 使用float32类型，保证计算精度和速度的平衡
 * - 数值稳定性: 所有中间变量都有合理的数值范围
 * - 溢出保护: 相位角自动限制在0~2π范围内
 * - 实时性: 整个宏的执行时间通常在5-20微秒内
 *
 * @param spll_obj 锁相环结构体对象 (注意：传入的是对象本身，不是指针)
 *                 类型: SPLL_3ph_DDSRF_F (结构体类型，非指针)
 *                 修改范围: 宏会修改结构体中的多个成员变量
 *                 内存访问: 直接访问结构体成员，无指针解引用开销
 *
 * 【使用示例】
 * @code
 * SPLL_3ph_DDSRF_F my_spll;
 *
 * // 1. 初始化
 * SPLL_3ph_DDSRF_F_init(50, 50e-6, 0.05, 0.94, &my_spll);
 *
 * // 2. 在ADC中断中的使用
 * void ADC_IRQHandler(void) {
 *     // 采样三相电压
 *     float Va = ADC_Read_Va();
 *     float Vb = ADC_Read_Vb();
 *     float Vc = ADC_Read_Vc();
 *
 *     // Clarke和Park变换
 *     ABC_DQ0_POS_NEG_MACRO(Va, Vb, Vc, my_spll.theta[0],
 *                           &my_spll.d_p, &my_spll.q_p,
 *                           &my_spll.d_n, &my_spll.q_n);
 *
 *     // 计算二倍频三角函数
 *     my_spll.cos_2theta = cosf(2.0f * my_spll.theta[0]);
 *     my_spll.sin_2theta = sinf(2.0f * my_spll.theta[0]);
 *
 *     // 执行锁相环运算
 *     SPLL_3ph_DDSRF_F_MACRO(my_spll);
 *
 *     // 获取结果
 *     float grid_phase = my_spll.theta[0];  // 电网相位角
 *     float grid_freq = my_spll.fo;         // 电网频率
 * }
 * @endcode
 *
 * 【性能对比】
 * - 函数版本: ~30-50微秒 (包含函数调用开销)
 * - 宏版本: ~10-25微秒 (纯计算时间)
 * - 性能提升: 约40-60% (取决于编译器优化级别)
 *
 * @note 使用前必须正确设置d_p, d_n, q_p, q_n和cos_2theta, sin_2theta
 * @note 此宏会修改结构体中的多个成员变量，确保数据一致性
 * @note 宏展开后代码行数较多，会增加程序存储器占用约1-2KB
 * @warning 宏参数不能有副作用，避免使用如spll_array[i++]这样的表达式
 * @warning 在多线程环境中使用时，需要确保对结构体的访问是原子的
 */
#define SPLL_3ph_DDSRF_F_MACRO(spll_obj)                                                                                                      \
    /* ===================================================================================== */                                                \
    /* ========== 解耦网络运算 (Decoupling Network Computation) ========== */                                                                  \
    /* ===================================================================================== */                                                \
    /* 【理论基础】正负序分量在同步旋转坐标系中会产生2ω频率的相互耦合干扰                    */                                                      \
    /* 通过解耦网络可以消除这种干扰，使正负序分量完全分离                                      */                                                      \
    /* 【数学原理】基于旋转坐标变换: [d_decoupl] = [d_original] - [耦合补偿项]              */                                                      \
    \
    /* 正序d轴分量解耦: 消除负序分量对正序d轴的2ω频率干扰 */                                                                                        \
    /* 数学公式: d_p_decoupl = d_p - d_n*cos(2θ) - q_n*sin(2θ) */                                                                             \
    /* 物理意义: 从原始正序d轴分量中减去负序分量在2θ坐标系中的投影 */                                                                                \
    /* 变量解析: d_p(原始正序d轴) - d_n_lpf*cos(2θ)(负序d轴在2θ系的d分量) - q_n*sin(2θ)(负序q轴在2θ系的d分量) */                                    \
    spll_obj.d_p_decoupl = spll_obj.d_p - (spll_obj.d_n_decoupl_lpf * spll_obj.cos_2theta) - (spll_obj.q_n_decoupl * spll_obj.sin_2theta);    \
    \
    /* 正序q轴分量解耦: 消除负序分量对正序q轴的2ω频率干扰 */                                                                                        \
    /* 数学公式: q_p_decoupl = q_p + d_n*sin(2θ) - q_n*cos(2θ) */                                                                             \
    /* 物理意义: 从原始正序q轴分量中减去负序分量在2θ坐标系中的投影 */                                                                                \
    /* 符号说明: +sin项是因为q轴与d轴正交，坐标变换时符号相反 */                                                                                    \
    /* 控制重要性: 这是锁相环的核心误差信号，解耦质量直接影响锁相性能 */                                                                              \
    spll_obj.q_p_decoupl = spll_obj.q_p + (spll_obj.d_n_decoupl_lpf * spll_obj.sin_2theta) - (spll_obj.q_n_decoupl * spll_obj.cos_2theta);    \
    \
    /* 负序d轴分量解耦: 消除正序分量对负序d轴的2ω频率干扰 */                                                                                        \
    /* 数学公式: d_n_decoupl = d_n - d_p*cos(2θ) + q_p*sin(2θ) */                                                                             \
    /* 物理意义: 从原始负序d轴分量中减去正序分量在2θ坐标系中的投影 */                                                                                \
    /* 应用价值: 提高负序分量检测精度，用于电网不平衡检测和补偿 */                                                                                  \
    spll_obj.d_n_decoupl = spll_obj.d_n - (spll_obj.d_p_decoupl_lpf * spll_obj.cos_2theta) + (spll_obj.q_p_decoupl * spll_obj.sin_2theta);    \
    \
    /* 负序q轴分量解耦: 消除正序分量对负序q轴的2ω频率干扰 */                                                                                        \
    /* 数学公式: q_n_decoupl = q_n - d_p*sin(2θ) - q_p*cos(2θ) */                                                                             \
    /* 物理意义: 从原始负序q轴分量中减去正序分量在2θ坐标系中的投影 */                                                                                \
    /* 扩展应用: 可用于负序无功功率检测和有源滤波器控制 */                                                                                          \
    spll_obj.q_n_decoupl = spll_obj.q_n - (spll_obj.d_p_decoupl_lpf * spll_obj.sin_2theta) - (spll_obj.q_p_decoupl * spll_obj.cos_2theta);    \
    \
    /* ===================================================================================== */                                                \
    /* ========== 低通滤波器运算 (Low Pass Filter Computation) ========== */                                                                  \
    /* ===================================================================================== */                                                \
    /* 【滤波器类型】一阶IIR (无限冲激响应) 低通滤波器                                          */                                                      \
    /* 【数学模型】差分方程: y[n] = k1*x[n] - k2*y[n-1]                                      */                                                      \
    /*           输出方程: output = y[n] + y[n-1]                                           */                                                      \
    /* 【设计目的】滤除高频噪声、开关谐波、残余2ω分量和ADC量化噪声                              */                                                      \
    /* 【性能指标】截止频率fc ≈ k1*fs/(2π*k2)，其中fs为采样频率                               */                                                      \
    \
    /* 正序d轴分量低通滤波处理 */                                                                                                                \
    /* 步骤1: 计算滤波器的当前状态 y[1] */                                                                                                      \
    /* 公式解析: y[1] = k1*input - k2*previous_state */                                                                                       \
    /* 物理意义: k1控制输入信号权重，k2控制历史状态权重 */                                                                                        \
    /* 稳定性: 要求k1>0, k2>0, k1+k2<1以保证滤波器稳定 */                                                                                      \
    spll_obj.y[1] = (spll_obj.d_p_decoupl * spll_obj.k1) - (spll_obj.y[0] * spll_obj.k2);                                                     \
    /* 步骤2: 计算滤波器输出 */                                                                                                                \
    /* 公式解析: output = current_state + previous_state */                                                                                   \
    /* 数学意义: 这种结构可以获得更好的相位特性和更低的群延迟 */                                                                                    \
    /* 应用价值: d_p_decoupl_lpf用于电网电压幅值检测和电压跌落保护 */                                                                             \
    spll_obj.d_p_decoupl_lpf = spll_obj.y[1] + spll_obj.y[0];                                                                                 \
    /* 步骤3: 状态更新，为下次计算准备 */                                                                                                        \
    /* 数据流: y[0] ← y[1]，将当前状态保存为下次的历史状态 */                                                                                     \
    spll_obj.y[0] = spll_obj.y[1];                                                                                                            \
    \
    /* 正序q轴分量低通滤波处理 */                                                                                                                \
    /* 控制重要性: 这是锁相环最关键的信号路径，滤波质量直接影响锁相性能 */                                                                          \
    /* 设计考虑: 需要平衡噪声抑制和动态响应，通常截止频率设为10-50Hz */                                                                            \
    spll_obj.x[1] = (spll_obj.q_p_decoupl * spll_obj.k1) - (spll_obj.x[0] * spll_obj.k2);                                                     \
    /* 误差信号: q_p_decoupl_lpf是锁相环的核心误差信号，送入PI控制器 */                                                                           \
    /* 控制目标: 通过反馈控制使q_p_decoupl_lpf趋于0，实现相位同步 */                                                                             \
    spll_obj.q_p_decoupl_lpf = spll_obj.x[1] + spll_obj.x[0];                                                                                 \
    spll_obj.x[0] = spll_obj.x[1];                                                                                                            \
    \
    /* 负序d轴分量低通滤波处理 */                                                                                                                \
    /* 应用场景: 用于电网不平衡检测，当|d_n_decoupl_lpf|>阈值时表示电网不平衡 */                                                                  \
    /* 精度要求: 负序分量通常较小(正常时<5%)，需要高精度滤波以提高检测灵敏度 */                                                                     \
    spll_obj.w[1] = (spll_obj.d_n_decoupl * spll_obj.k1) - (spll_obj.w[0] * spll_obj.k2);                                                     \
    /* 故障诊断: d_n_decoupl_lpf可用于电网故障检测和保护算法 */                                                                                   \
    /* 补偿控制: 在有源滤波器中用于负序电压补偿控制 */                                                                                            \
    spll_obj.d_n_decoupl_lpf = spll_obj.w[1] + spll_obj.w[0];                                                                                 \
    spll_obj.w[0] = spll_obj.w[1];                                                                                                            \
    \
    /* 负序q轴分量低通滤波处理 */                                                                                                                \
    /* 扩展功能: 可用于负序无功功率计算: Q_neg = d_n*q_n_lpf - q_n*d_n_lpf */                                                                   \
    /* 控制应用: 在不平衡补偿控制中，q_n_decoupl_lpf用于负序相位控制 */                                                                           \
    spll_obj.z[1] = (spll_obj.q_n_decoupl * spll_obj.k1) - (spll_obj.z[0] * spll_obj.k2);                                                     \
    /* 监测价值: 可用于电网质量监测，评估电网的不平衡程度 */                                                                                        \
    spll_obj.q_n_decoupl_lpf = spll_obj.z[1] + spll_obj.z[0];                                                                                 \
    spll_obj.z[0] = spll_obj.z[1];                                                                                                            \
    \
    /* ===================================================================================== */                                                \
    /* ========== 环路滤波器运算 (Loop Filter Computation) ========== */                                                                      \
    /* ===================================================================================== */                                                \
    /* 【控制理论基础】环路滤波器是锁相环的核心控制器，通常采用PI控制器结构                      */                                                      \
    /* 【传递函数】H(z) = (B0 + B1*z^-1) / (1 + A1*z^-1)                                   */                                                      \
    /* 【差分方程】y[n] = y[n-1] + B0*e[n] + B1*e[n-1]                                     */                                                      \
    /* 【设计目标】将相位误差转换为频率控制信号，决定锁相环的动态响应特性                        */                                                      \
    \
    /* 步骤1: 设置环路滤波器的输入信号 */                                                                                                        \
    /* 信号来源: q_p_decoupl是经过解耦但未滤波的正序q轴分量 */                                                                                   \
    /* 选择原因: 使用未滤波信号可以获得更快的动态响应 */                                                                                          \
    /* 物理意义: v_q[0]直接反映电网相位与本地相位的瞬时偏差 */                                                                                    \
    /* 控制目标: 通过PI控制使v_q[0]趋于0，实现相位同步 */                                                                                        \
    spll_obj.v_q[0] = spll_obj.q_p_decoupl;  /* 使用正序q轴分量作为相位误差信号 */                                                               \
    \
    /* 步骤2: PI控制器运算 - 将相位误差转换为频率控制信号 */                                                                                      \
    /* 数学公式: ylf[0] = ylf[1] + B0*v_q[0] + B1*v_q[1] */                                                                                   \
    /* 公式解析: */                                                                                                                            \
    /*   ylf[1]: 前一次的频率控制信号 (积分项的历史值) */                                                                                         \
    /*   B0*v_q[0]: 当前相位误差的比例项 (Kp*error) */                                                                                         \
    /*   B1*v_q[1]: 前一次相位误差的积分项 (Ki*error*T) */                                                                                      \
    /* 控制作用: 比例项提供快速响应，积分项消除稳态误差 */                                                                                        \
    /* 系数设计: B0决定响应速度，B1决定稳态精度，需要平衡稳定性和性能 */                                                                           \
    spll_obj.ylf[0] = spll_obj.ylf[1] + (spll_obj.lpf_coeff.B0_lf * spll_obj.v_q[0]) + (spll_obj.lpf_coeff.B1_lf * spll_obj.v_q[1]);          \
    \
    /* 步骤3: 状态更新，为下次计算准备历史数据 */                                                                                                \
    /* 频率控制信号状态更新: ylf[1] ← ylf[0] */                                                                                                \
    /* 数据流向: 当前输出保存为下次计算的历史值 */                                                                                              \
    spll_obj.ylf[1] = spll_obj.ylf[0];                                                                                                        \
    /* 相位误差信号状态更新: v_q[1] ← v_q[0] */                                                                                               \
    /* 重要性: PI控制器需要当前和前一次的误差信号进行计算 */                                                                                      \
    spll_obj.v_q[1] = spll_obj.v_q[0];                                                                                                        \
    \
    /* ===================================================================================== */                                                \
    /* ========== VCO (压控振荡器) 运算 (Voltage Controlled Oscillator) ========== */                                                         \
    /* ===================================================================================== */                                                \
    /* 【VCO原理】压控振荡器将频率控制信号转换为相位角，是锁相环的最后一个环节                    */                                                      \
    /* 【数学模型】相位积分方程: θ[n] = θ[n-1] + 2π*f*ΔT                                      */                                                      \
    /* 【物理意义】频率是相位的时间导数，积分频率得到相位                                        */                                                      \
    /* 【设计考虑】包含频率自适应滤波，提高电网电压跳变时的稳定性                                */                                                      \
    \
    /* 步骤1: 频率计算与自适应滤波 */                                                                                                            \
    /* 原始公式: fo = fn + ylf[0] (直接相加，响应快但可能引起抖动) */                                                                             \
    /* spll_obj.fo = spll_obj.fn + spll_obj.ylf[0]; */                      /* 原始频率计算公式 */                                              \
    /* 改进公式: 采用一阶低通滤波器对频率进行平滑处理 */                                                                                          \
    /* 数学表达: fo += (target_freq - fo) * α */                                                                                              \
    /* 其中: target_freq = fn + ylf[0], α = 0.0001 (自适应系数) */                                                                            \
    /* 工程意义: α值越小频率越平滑但响应越慢，α值越大响应越快但可能抖动 */                                                                         \
    /* 应用场景: 电网电压跳变时防止频率突变导致的相位偏差和系统不稳定 */                                                                           \
    /* 系数选择: 0.0001是经验值，可根据实际电网条件调整 (范围: 0.00001~0.001) */                                                                \
    spll_obj.fo += ((spll_obj.fn + spll_obj.ylf[0]) - spll_obj.fo) * 0.0001f; /* 频率自适应滤波: 电压跳变时防止频率抖动导致相位偏差 */            \
    \
    /* 步骤2: 相位积分运算 - VCO的核心功能 */                                                                                                   \
    /* 数学公式: θ[n] = θ[n-1] + 2π*f*ΔT */                                                                                                    \
    /* 公式推导: 从 dθ/dt = 2π*f 积分得到 θ = ∫2π*f*dt ≈ θ_prev + 2π*f*ΔT */                                                                 \
    /* 变量解析: */                                                                                                                            \
    /*   theta[1]: 前一次的相位角 (弧度) */                                                                                                     \
    /*   fo: 当前检测到的电网频率 (Hz) */                                                                                                       \
    /*   delta_T: 采样周期 (秒) */                                                                                                             \
    /*   2π: 将频率(Hz)转换为角频率(rad/s)的系数 */                                                                                             \
    /* 数值精度: 使用float32类型，相位精度约为 ±0.0001 弧度 */                                                                                  \
    /* 积分误差: 长期运行可能累积误差，通过相位限制来避免溢出 */                                                                                  \
    spll_obj.theta[0] = spll_obj.theta[1] + ((spll_obj.fo * spll_obj.delta_T) * (float32)(2.0 * 3.1415926));                                  \
    \
    /* 步骤3: 相位角限制 - 防止数值溢出和保持周期性 */                                                                                           \
    /* 数学原理: 相位角具有2π周期性，θ和θ+2π在物理上等价 */                                                                                     \
    /* 限制范围: 0 ≤ θ < 2π (标准相位角范围) */                                                                                                \
    /* 溢出处理: 当θ ≥ 2π时，减去2π使其回到标准范围 */                                                                                          \
    /* 数值稳定性: 防止长期运行时相位角无限增大导致的数值溢出 */                                                                                  \
    /* 计算精度: 使用高精度π值 (3.1415926) 保证角度计算精度 */                                                                                  \
    if (spll_obj.theta[0] > (float32)(2.0 * 3.1415926))                                                                                       \
        spll_obj.theta[0] = spll_obj.theta[0] - (float32)(2.0 * 3.1415926);                                                                   \
    \
    /* 步骤4: 状态更新 - 为下次计算准备历史数据 */                                                                                              \
    /* 数据流向: theta[1] ← theta[0] */                                                                                                        \
    /* 重要性: VCO积分需要前一次的相位值进行递推计算 */                                                                                          \
    /* 初始化: 系统启动时theta[1]应初始化为0 */                                                                                                \
    /* 连续性: 保证相位角的连续性和积分的正确性 */                                                                                              \
    spll_obj.theta[1] = spll_obj.theta[0];  /* 保存当前值作为下次计算的前一值 */

/*
 * =====================================================================================
 * 【总结与使用指导】
 * =====================================================================================
 *
 * 1. 【初始化步骤】
 *    SPLL_3ph_DDSRF_F my_spll;
 *    SPLL_3ph_DDSRF_F_init(50, 50e-6, 0.05, 0.94, &my_spll);
 *
 * 2. 【运行时调用】(在ADC中断中)
 *    // 准备输入数据
 *    ABC_DQ0_POS_NEG_MACRO(Va, Vb, Vc, my_spll.theta[0],
 *                          &my_spll.d_p, &my_spll.q_p,
 *                          &my_spll.d_n, &my_spll.q_n);
 *    my_spll.cos_2theta = cosf(2.0f * my_spll.theta[0]);
 *    my_spll.sin_2theta = sinf(2.0f * my_spll.theta[0]);
 *
 *    // 执行锁相环运算
 *    SPLL_3ph_DDSRF_F_MACRO(my_spll);
 *
 *    // 获取结果
 *    float grid_phase = my_spll.theta[0];  // 电网相位角 (0~2π)
 *    float grid_freq = my_spll.fo;         // 电网频率 (Hz)
 *
 * 3. 【参数调试指导】
 *    - k1, k2: 影响滤波器性能
 *      * 噪声大: k1=0.01, k2=0.98 (强滤波)
 *      * 响应快: k1=0.1, k2=0.89 (弱滤波)
 *    - B0_lf, B1_lf: 影响锁相环动态性能
 *      * 快速锁定: 增大B0_lf
 *      * 稳定性好: 减小B0_lf, 增大B1_lf
 *
 * 4. 【性能指标】
 *    - 锁相时间: 2-5个电网周期 (40-100ms @50Hz)
 *    - 相位精度: ±0.1° (±0.0017 rad)
 *    - 频率精度: ±0.01 Hz
 *    - 执行时间: 10-25μs (宏版本)
 *
 * 5. 【故障诊断】
 *    - |q_p_decoupl_lpf| > 0.1: 锁相失败或电网异常
 *    - |d_n_decoupl_lpf| > 0.05: 电网不平衡
 *    - |fo - fn| > 2: 电网频率异常
 *
 * 6. 【注意事项】
 *    - 必须在初始化后使用
 *    - 输入数据必须是标幺值
 *    - 采样频率应 > 10倍电网频率
 *    - 三角函数计算需要高精度
 *
 * =====================================================================================
 */

#endif /* SPLL_3ph_DDSRF_F_H_ */
